/* eslint-disable max-lines */
import styled from '@emotion/styled';
import {Flex} from 'antd';
import {Button} from '@panda-design/components';
import {MCPSquareLink, MCPPlaygroundLink} from '@/links/mcp';

const Container = styled.div`
    width: 100%;
    min-height: calc(100vh - 48px);
    background: #fff;
    position: relative;
`;

const TopNavArea = styled(Flex)`
    position: absolute;
    top: 20px;
    right: 56px;
    gap: 24px;
`;

const NavLink = styled.a`
    color: #1890ff;
    text-decoration: none;
    font-size: 14px;
    
    &:hover {
        color: #40a9ff;
        text-decoration: none;
    }
`;

const MainTitleContainer = styled.div`
    position: absolute;
    margin-top: 64px;
    margin-left: 56px;
`;

const MainTitle = styled.h1`
    font-weight: 900;
    font-size: 46px;
    line-height: 50px;
    margin: 0;
    color: #000;
`;

const SubTitle = styled.h2`
    font-weight: 700;
    font-size: 32px;
    line-height: 50px;
    margin: 0;
    color: #000;
`;

const Description = styled.p`
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #8D8D8D;
    margin: 16px 0 0 0;
`;

const ButtonArea = styled(Flex)`
    margin-top: 40px;
    gap: 16px;
`;

const FeaturesContainer = styled(Flex)`
    margin-top: 359px;
    margin-left: 56px;
    margin-right: 56px;
    gap: 24px;
`;

const FeatureCard = styled.div`
    width: 270px;
    height: 144px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 24px;
    background: #fff;
    
    &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
`;

const FeatureTitle = styled.h3`
    font-size: 16px;
    font-weight: 600;
    color: #000;
    margin: 0 0 12px 0;
`;

const FeatureContent = styled.p`
    font-size: 14px;
    color: #666;
    line-height: 20px;
    margin: 0;
`;

const ProcessContainer = styled(Flex)`
    margin: 40px 56px 0 56px;
    gap: 24px;
`;

const ProcessCard = styled.div`
    flex: 1;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    background: #fff;
`;

const ProcessHeader = styled(Flex)`
    width: 556px;
    height: 66px;
    background: linear-gradient(#E5F2FF 100%, #FFFEF0CC 80%);
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    border-radius: 8px 8px 0 0;
`;

const ProcessTitle = styled.h3`
    font-size: 16px;
    font-weight: 600;
    color: #000;
    margin: 0;
`;

const ProcessSteps = styled.div`
    padding: 24px;
`;

const ProcessStep = styled.div`
    margin-bottom: 20px;
    
    &:last-child {
        margin-bottom: 0;
    }
`;

const StepTitle = styled.h4`
    font-size: 14px;
    font-weight: 600;
    color: #000;
    margin: 0 0 8px 0;
`;

const StepDescription = styled.p`
    font-size: 14px;
    color: #666;
    line-height: 20px;
    margin: 0;
`;

const features = [
    {
        title: '降低双边成本',
        content: '为供需双方降本，实现开发、接入、调用成本优化',
    },
    {
        title: '协议无缝转换',
        content: '统一转换层，支持MCP、OpenAPI、脚本快速转换',
    },
    {
        title: '工具精准调用',
        content: '提供多维调试入口，帮助用户发现、高效调用工具',
    },
    {
        title: '构建完整生态',
        content: '打造服务到编排的闭环，赋能开发者与业务方创新',
    },
];

const producerSteps = [
    {
        title: '便捷注册',
        description: '快速将MCP或OpenAPI封装为MCP Server，完成接入',
    },
    {
        title: '灵活配置',
        description: '自定义工具参数与模板，确保模型能高效调用',
    },
    {
        title: '在线调试',
        description: '配置后可在线调试验证，查看返回结果，确保可靠',
    },
    {
        title: '发布共享',
        description: '将MCP Server发布到广场，分享生态提升服务价值',
    },
];

const consumerSteps = [
    {
        title: '广场发现',
        description: '在广场多维度探索筛选MCP，精准匹配业务需求',
    },
    {
        title: '组合试用',
        description: '在Playground组合试用，与大模型交互评估效果',
    },
    {
        title: '一键订阅',
        description: '按需创建应用并订阅MCP，灵活按需配置工具',
    },
    {
        title: '集成调用',
        description: '将已订阅工具轻松集成至AI Agent，即刻扩展能力',
    },
];

const AIToolsHome = () => {
    return (
        <Container>
            <TopNavArea>
                <NavLink href="#">使用文档</NavLink>
                <NavLink href="#">介绍文档</NavLink>
            </TopNavArea>

            <MainTitleContainer>
                <MainTitle>万千工具一网打尽</MainTitle>
                <SubTitle>智能体从此有求必应</SubTitle>
                <Description>
                    厂内MCP服务中心：一站式解决AI Agent工具生产与调用难题
                </Description>
                <ButtonArea>
                    <MCPSquareLink>
                        <Button type="primary">去MCP广场体验</Button>
                    </MCPSquareLink>
                    <MCPPlaygroundLink>
                        <Button>去Playground试用</Button>
                    </MCPPlaygroundLink>
                </ButtonArea>
            </MainTitleContainer>

            <FeaturesContainer>
                {features.map((feature, index) => (
                    <FeatureCard key={index}>
                        <FeatureTitle>{feature.title}</FeatureTitle>
                        <FeatureContent>{feature.content}</FeatureContent>
                    </FeatureCard>
                ))}
            </FeaturesContainer>

            <ProcessContainer>
                <ProcessCard>
                    <ProcessHeader>
                        <ProcessTitle>工具生产者使用流程</ProcessTitle>
                        <Button type="text" size="small">查看详情</Button>
                    </ProcessHeader>
                    <ProcessSteps>
                        {producerSteps.map((step, index) => (
                            <ProcessStep key={index}>
                                <StepTitle>{step.title}</StepTitle>
                                <StepDescription>{step.description}</StepDescription>
                            </ProcessStep>
                        ))}
                    </ProcessSteps>
                </ProcessCard>

                <ProcessCard>
                    <ProcessHeader>
                        <ProcessTitle>工具消费者使用流程</ProcessTitle>
                        <Button type="text" size="small">查看详情</Button>
                    </ProcessHeader>
                    <ProcessSteps>
                        {consumerSteps.map((step, index) => (
                            <ProcessStep key={index}>
                                <StepTitle>{step.title}</StepTitle>
                                <StepDescription>{step.description}</StepDescription>
                            </ProcessStep>
                        ))}
                    </ProcessSteps>
                </ProcessCard>
            </ProcessContainer>
        </Container>
    );
};

export default AIToolsHome;
